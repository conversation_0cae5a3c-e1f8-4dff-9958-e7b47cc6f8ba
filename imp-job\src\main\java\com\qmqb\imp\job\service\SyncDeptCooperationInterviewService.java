package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.util.TypeUtils;
import com.hzed.structure.common.exception.ServiceException;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.enums.DingTalkProcessInstanceStatusEnum;
import com.qmqb.imp.job.api.client.DingTalkApiClient;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.qmqb.imp.system.domain.process.ProcessDeptCooperationInterview;
import com.qmqb.imp.system.mapper.process.ProcessDeptCooperationInterviewMapper;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.system.service.ISysUserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 同步部门合作度访谈数据定时任务
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncDeptCooperationInterviewService {

    private final DingTalkApiClient dingTalkApiClient;
    private final ProcessDeptCooperationInterviewMapper processDeptCooperationInterviewMapper;
    private final ISysUserService sysUserService;

    private static final long ONE_DAY_MILLIS = 86400000;
    private static final String PROCESS_CODE = "PROC-FB63ABE9-606D-48FB-A3E0-6743817FDA7E";

    private final Map<String, SysUser> userIdToUser = new ConcurrentHashMap<>();
    private final Map<String, SysUser> nickNameToUser = new ConcurrentHashMap<>();

    @TraceId("同步部门合作度访谈数据定时任务")
    @XxlJob("syncDeptCooperationInterviewJobHandler")
    public ReturnT<String> syncDeptCooperationInterviewJobHandler(String dayAgo) {
        if (StringUtils.isBlank(dayAgo)) {
            dayAgo = "2";
        }

        try {
            XxlJobLogger.log("开始执行同步部门合作度访谈数据定时任务...");
            log.info("开始执行同步部门合作度访谈数据定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();

            syncDeptCooperationInterview(dayAgo);

            sw.stop();
            XxlJobLogger.log("同步部门合作度访谈数据定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("同步部门合作度访谈数据定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("同步部门合作度访谈数据定时任务执行异常", e);
            throw new ServiceException("同步部门合作度访谈数据定时任务执行异常;error:"+e.getMessage(), e);
        }
    }

    /**
     * 同步部门合作度访谈数据
     */
    private void syncDeptCooperationInterview(String dayAgo) {
        long now = System.currentTimeMillis();
        List<String> processInstanceIds = dingTalkApiClient.listProcessInstanceIds(
            PROCESS_CODE,
            now - ONE_DAY_MILLIS * Integer.parseInt(dayAgo),
            now,
            0L,
            Collections.emptyList()
        );

        log.info("获取到部门合作度访谈流程实例ID列表:{}", processInstanceIds);

        if (processInstanceIds.isEmpty()) {
            log.info("没有找到需要同步的部门合作度访谈数据");
            return;
        }

        List<ProcessDeptCooperationInterview> batchInsertArgs = new ArrayList<>();

        List<SysUser> sysUsers = sysUserService.selectAllUser2();
        userIdToUser.putAll(sysUsers.stream()
                .filter(item->StringUtils.isNotBlank(item.getDingtalkUserId()))
                .collect(Collectors.toMap(SysUser::getDingtalkUserId, Function.identity(),(v1,v2)->{
                    if(UserConstants.USER_NORMAL.equals(v1.getDelFlag())){
                        return v1;
                    }else {
                        return v2;
                    }
                })));
        nickNameToUser.putAll(sysUsers.stream()
                .filter(item->StringUtils.isNotBlank(item.getNickName()))
                .collect(Collectors.toMap(SysUser::getNickName, Function.identity(),(v1,v2) -> {
                    if(UserConstants.USER_NORMAL.equals(v1.getDelFlag())){
                        return v1;
                    }else {
                        return v2;
                    }
                })));

        for (String processInstanceId : processInstanceIds) {
            try {
                GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult pi = dingTalkApiClient.getProcessInstance(processInstanceId);

                // 只处理已完成且审批通过的流程
                if (!DingTalkProcessInstanceStatusEnum.COMPLETED.getName().equals(pi.status)) {
                    continue;
                }

                List<ProcessDeptCooperationInterview> interviews = parseProcessInstance(pi);
                if (CollUtil.isNotEmpty(interviews)) {
                    batchInsertArgs.addAll(interviews);
                }
            } catch (Exception e) {
                log.error("处理流程实例[{}]时发生异常", processInstanceId, e);
            }
        }

        if (!batchInsertArgs.isEmpty()) {
            processDeptCooperationInterviewMapper.batchInsertOnUpdate(batchInsertArgs);
            log.info("成功同步部门合作度访谈数据{}条", batchInsertArgs.size());
        }
    }

    /**
     * 解析流程实例数据
     */
    private List<ProcessDeptCooperationInterview> parseProcessInstance(GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult pi) {
        List<ProcessDeptCooperationInterview> result = new ArrayList<>();

        // 获取基础信息
        String approvalId = pi.businessId;
        String approvalResult = pi.result;
        Date submitTime = TypeUtils.castToDate(pi.createTime);
        String submitterUserId = pi.originatorUserId;
        SysUser submitterUser = userIdToUser.get(submitterUserId);
        if(submitterUser == null || StringUtils.isBlank(submitterUser.getNickName())){
            return result;
        }
        String submitterName = submitterUser.getNickName();
        String submitterGroup = Optional.ofNullable(submitterUser.getDept()).map(SysDept::getDeptName).orElse(null);
        Long submitterGroupId = Optional.ofNullable(submitterUser.getDept()).map(SysDept::getDeptId).orElse(null);

        // 解析表单字段
        Map<String, String> formValues = new HashMap<>(16);
        for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues component : pi.formComponentValues) {
            if (StrUtil.isNotBlank(component.name) && StrUtil.isNotBlank(component.value)) {
                formValues.put(component.name, component.value);
            }
        }

        // 获取年份和月份
        Integer year =  formValues.get("年份")!=null?Integer.parseInt(formValues.get("年份")):null;
        Integer month = formValues.get("月份")!=null?Integer.parseInt(formValues.get("月份")):null;

        // 解析人员数据 - 表扬类型
        parseEmployeeData(result, formValues, approvalId, year, month, "1",
            Arrays.asList("值得表扬的同事1", "值得表扬的同事2"),
            Arrays.asList("事项说明1", "事项说明2"),
            submitterName, submitterGroupId, submitterGroup, approvalResult, submitTime);

        // 解析人员数据 - 待改进类型
        parseEmployeeData(result, formValues, approvalId, year, month, "0",
            Arrays.asList("工作上存在问题的同事1", "工作上存在问题的同事2"),
            Arrays.asList("事项&问题说明1", "事项&问题说明2"),
            submitterName, submitterGroupId, submitterGroup, approvalResult, submitTime);

        return result;
    }

    /**
     * 解析员工数据
     */
    private void parseEmployeeData(List<ProcessDeptCooperationInterview> result,
                                   Map<String, String> formValues,
                                   String approvalId, Integer year, Integer month, String type,
                                   List<String> employeeFields, List<String> descriptionFields,
                                   String submitterName, Long submitterGroupId, String submitterGroup,
                                   String approvalResult, Date submitTime) {

        for (int i = 0; i < employeeFields.size(); i++) {
            String employeeName = formValues.get(employeeFields.get(i));
            String description = formValues.get(descriptionFields.get(i));

            if (StrUtil.isNotBlank(employeeName)) {
                ProcessDeptCooperationInterview interview = new ProcessDeptCooperationInterview();
                interview.setApprovalId(approvalId);
                interview.setYearValue(year);
                interview.setMonthValue(month);
                interview.setType(type);
                interview.setEmployeeName(employeeName);
                interview.setDescription(description);
                interview.setSubmitterName(submitterName);
                interview.setSubmitterGroupId(submitterGroupId);
                interview.setSubmitterGroup(submitterGroup);
                interview.setApprovalResult(approvalResult);
                interview.setSubmitTime(submitTime);
                interview.setDelFlag(0);

                // 通过绩效系统匹配获取组别和岗位
                enrichEmployeeInfo(interview, employeeName, year, month);

                result.add(interview);
            }
        }
    }

    /**
     * 通过绩效系统匹配获取员工组别和岗位信息
     */
    private void enrichEmployeeInfo(ProcessDeptCooperationInterview interview, String employeeName, Integer year, Integer month) {
        // 通过绩效系统匹配获取组别和岗位
        SysUser user = nickNameToUser.get(employeeName);
        if(user != null){
            interview.setGroupId(user.getDeptId());
            interview.setGroupName(Optional.ofNullable(user.getDept()).map(SysDept::getDeptName).orElse(null));
            interview.setEmployeePosition(user.getRoles().stream().findFirst().map(SysRole::getRoleName).orElse(null));
            interview.setEmployeePositionType(mapRoleToPersonType(user));
        }
    }

    /**
     * 根据用户角色映射为PersonTypeEnum类型
     * 参考PersonTypeEnum的定义进行映射
     */
    private String mapRoleToPersonType(SysUser user) {
        if (user == null || user.getRoles() == null || user.getRoles().isEmpty()) {
            return null;
        }

        // 获取用户的第一个角色ID
        Long roleId = user.getRoles().stream()
            .findFirst()
            .map(SysRole::getRoleId)
            .orElse(null);

        if (roleId == null) {
            return null;
        }

        // 根据PersonTypeEnum的roleId映射
        if (PersonTypeEnum.TECHNICAL_MANAGER.getRoleId().equals(roleId)) {
            return PersonTypeEnum.TECHNICAL_MANAGER.getType().toString();
        } else if (PersonTypeEnum.DEVELOPER.getRoleId().equals(roleId)) {
            return PersonTypeEnum.DEVELOPER.getType().toString();
        } else if (PersonTypeEnum.TESTER.getRoleId().equals(roleId)) {
            return PersonTypeEnum.TESTER.getType().toString();
        } else if (PersonTypeEnum.PROJECT_MANAGER.getRoleId().equals(roleId)) {
            return PersonTypeEnum.PROJECT_MANAGER.getType().toString();
        } else if (PersonTypeEnum.OPERATOR.getRoleId().equals(roleId)) {
            return PersonTypeEnum.OPERATOR.getType().toString();
        }

        // 默认返回开发人员类型
        return null;
    }
}
