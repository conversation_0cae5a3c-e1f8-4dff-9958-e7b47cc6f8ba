package com.qmqb.imp.job.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzed.structure.common.exception.ServiceException;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.ZtProduct;
import com.qmqb.imp.system.domain.ZtProductplan;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.ZtProductMapper;
import com.qmqb.imp.system.mapper.ZtProductplanMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品计划钉钉提醒服务
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ProductPlanWarnService {

    private final ZtProductMapper ztProductMapper;
    private final ZtProductplanMapper ztProductplanMapper;
    private final DingTalkConfig dingTalkConfig;
    private final IMessageService messageService;

    /**
     * 产品计划钉钉提醒定时任务
     * 每个工作日上午11点执行
     *
     * @param param 参数
     * @return 执行结果
     */
    @TraceId("产品计划钉钉提醒")
    @XxlJob("productPlanWarnJobHandler")
    public ReturnT<String> productPlanWarnJobHandler(String param) {
        log.info("开始执行产品计划钉钉提醒任务，参数：{}", param);
        try {
            Date nowDate = DateUtils.getNowDate();
            // 节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, nowDate))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }

            // 1. 检查没有计划的产品
            List<String> noPlanProducts = checkProductsWithoutPlan();
            String noPlanContent = buildNoPlanContent(noPlanProducts);

            // 2. 检查待启动的产品计划
            List<String> pendingStartPlans = checkPendingStartPlans(nowDate);
            String pendingStartContent = buildPendingStartContent(pendingStartPlans);

            // 3. 检查待结束的产品计划
            List<String> pendingEndPlans = checkPendingEndPlans(nowDate);
            String pendingEndContent = buildPendingEndContent(pendingEndPlans);

            // 发送钉钉消息（按模块分配）
            sendDingTalkMessageByModules(noPlanContent, pendingStartContent, pendingEndContent);

            log.info("产品计划钉钉提醒任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("产品计划钉钉提醒任务执行失败，异常信息：", e);
            throw new ServiceException("产品计划钉钉提醒任务执行失败：" + e.getMessage(),e);
        }
    }

    /**
     * 检查没有计划的产品列表
     * 仅检查2025年6月份之后创建的产品
     *
     * @return 没有计划的产品名称列表
     */
    @DS("zentao")
    private List<String> checkProductsWithoutPlan() {
        log.info("开始检查没有计划的产品列表");
        // 2025年6月1日
        Date june2025 = DateUtils.parseDate("2025-06-01");

        // 查询2025年6月份之后创建的产品，添加查询条件
        LambdaQueryWrapper<ZtProduct> productWrapper = Wrappers.<ZtProduct>lambdaQuery()
            .ge(ZtProduct::getCreatedDate, june2025)
            .eq(ZtProduct::getDeleted, "0")
            .eq(ZtProduct::getStatus, "normal")
            .eq(ZtProduct::getShadow, 0)
            .eq(ZtProduct::getAcl, "open")
            .orderByAsc(ZtProduct::getCreatedDate);
        List<ZtProduct> products = ztProductMapper.selectList(productWrapper);
        log.info("查询到符合条件的产品数量：{}", products.size());

        if (CollectionUtils.isEmpty(products)) {
            return new ArrayList<>();
        }

        // 获取所有产品ID
        List<Integer> productIds = products.stream()
            .map(ZtProduct::getId)
            .collect(Collectors.toList());

        // 查询有计划的产品ID（使用自定义SQL绕过数据权限拦截器）
        List<ZtProductplan> plans = ztProductplanMapper.selectByProductIds(productIds);
        log.info("查询到的产品计划数量：{}", plans.size());

        Set<Integer> productsWithPlan = plans.stream()
            .map(ZtProductplan::getProduct)
            .collect(Collectors.toSet());

        // 返回没有计划的产品名称
        List<String> noPlanProducts = products.stream()
            .filter(product -> !productsWithPlan.contains(product.getId()))
            .map(ZtProduct::getName)
            .collect(Collectors.toList());
        log.info("没有计划的产品数量：{}", noPlanProducts.size());
        return noPlanProducts;
    }

    /**
     * 检查开始日期<今天并且状态为【未开始】的产品计划
     * 仅检查2025年创建的计划
     *
     * @param nowDate 当前日期
     * @return 待启动的产品计划列表（格式：产品名---计划名）
     */
    @DS("zentao")
    private List<String> checkPendingStartPlans(Date nowDate) {
        log.info("开始检查待启动的产品计划，当前日期：{}", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, nowDate));
        // 查询开始日期小于今天且状态为wait的计划（使用自定义SQL绕过数据权限拦截器）
        List<ZtProductplan> plans = ztProductplanMapper.selectPendingStartPlans(nowDate);
        log.info("查询到待启动的产品计划数量：{}", plans.size());

        if (CollectionUtils.isEmpty(plans)) {
            return new ArrayList<>();
        }

        // 获取产品信息
        List<Integer> productIds = plans.stream()
            .map(ZtProductplan::getProduct)
            .distinct()
            .collect(Collectors.toList());

        LambdaQueryWrapper<ZtProduct> productWrapper = Wrappers.<ZtProduct>lambdaQuery()
            .in(ZtProduct::getId, productIds)
            .eq(ZtProduct::getDeleted, "0")
            .eq(ZtProduct::getStatus, "normal")
            .eq(ZtProduct::getShadow, 0)
            .eq(ZtProduct::getAcl, "open");
        List<ZtProduct> products = ztProductMapper.selectList(productWrapper);
        log.info("查询到符合条件的产品数量：{}", products.size());

        Map<Integer, String> productNameMap = products.stream()
            .collect(Collectors.toMap(ZtProduct::getId, ZtProduct::getName));

        // 组装结果，按开始日期排序（越早越前）
        List<String> pendingStartPlans = plans.stream()
            .filter(plan -> productNameMap.containsKey(plan.getProduct()))
            .sorted((p1, p2) -> {
                // 按开始日期升序排序，null值放在最后
                Date begin1 = p1.getBegin();
                Date begin2 = p2.getBegin();
                if (begin1 == null && begin2 == null) {return 0;}
                if (begin1 == null) {return 1;}
                if (begin2 == null) {return -1;}
                return begin1.compareTo(begin2);
            })
            .map(plan -> {
                String dateStr = plan.getBegin() != null ?
                    DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, plan.getBegin()) : "无日期";
                return productNameMap.get(plan.getProduct()) + "---" + plan.getTitle() + "（开始日期：" + dateStr + "）";
            })
            .collect(Collectors.toList());
        log.info("最终待启动的产品计划数量：{}", pendingStartPlans.size());
        return pendingStartPlans;
    }

    /**
     * 检查结束日期<今天并且状态不为结束的产品计划
     * 仅检查2025年创建的计划
     *
     * @param nowDate 当前日期
     * @return 待结束的产品计划列表（格式：产品名---计划名）
     */
    @DS("zentao")
    private List<String> checkPendingEndPlans(Date nowDate) {
        log.info("开始检查待结束的产品计划，当前日期：{}", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, nowDate));
        // 查询结束日期小于今天且状态不为done和closed的计划（使用自定义SQL绕过数据权限拦截器）
        List<ZtProductplan> plans = ztProductplanMapper.selectPendingEndPlans(nowDate);
        log.info("查询到待结束的产品计划数量：{}", plans.size());

        if (CollectionUtils.isEmpty(plans)) {
            return new ArrayList<>();
        }

        // 获取产品信息
        List<Integer> productIds = plans.stream()
            .map(ZtProductplan::getProduct)
            .distinct()
            .collect(Collectors.toList());

        LambdaQueryWrapper<ZtProduct> productWrapper = Wrappers.<ZtProduct>lambdaQuery()
            .in(ZtProduct::getId, productIds)
            .eq(ZtProduct::getDeleted, "0")
            .eq(ZtProduct::getStatus, "normal")
            .eq(ZtProduct::getShadow, 0)
            .eq(ZtProduct::getAcl, "open");
        List<ZtProduct> products = ztProductMapper.selectList(productWrapper);
        log.info("查询到符合条件的产品数量：{}", products.size());

        Map<Integer, String> productNameMap = products.stream()
            .collect(Collectors.toMap(ZtProduct::getId, ZtProduct::getName));

        // 组装结果，按结束日期排序（越早越前）
        List<String> pendingEndPlans = plans.stream()
            .filter(plan -> productNameMap.containsKey(plan.getProduct()))
            .sorted((p1, p2) -> {
                // 按结束日期升序排序，null值放在最后
                Date end1 = p1.getEnd();
                Date end2 = p2.getEnd();
                if (end1 == null && end2 == null){ return 0;}
                if (end1 == null) {return 1;}
                if (end2 == null) {return -1;}
                return end1.compareTo(end2);
            })
            .map(plan -> {
                String dateStr = plan.getEnd() != null ?
                    DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, plan.getEnd()) : "无日期";
                return productNameMap.get(plan.getProduct()) + "---" + plan.getTitle() + "（结束日期：" + dateStr + "）";
            })
            .collect(Collectors.toList());
        log.info("最终待结束的产品计划数量：{}", pendingEndPlans.size());
        return pendingEndPlans;
    }

    /**
     * 构建无计划产品的消息内容
     *
     * @param noPlanProducts 无计划的产品列表
     * @return 消息内容，如果列表为空则返回空字符串
     */
    private String buildNoPlanContent(List<String> noPlanProducts) {
        if (CollectionUtils.isEmpty(noPlanProducts)) {
            return "";
        }

        StringBuilder content = new StringBuilder();
        content.append("【无计划】有以下产品没有任何计划，请安排计划并跟进。\n");
        content.append("----------------\n");
        for (int i = 0; i < noPlanProducts.size(); i++) {
            content.append(i + 1).append(".").append(noPlanProducts.get(i)).append("\n");
        }
        return content.toString();
    }

    /**
     * 构建待启动计划的消息内容
     *
     * @param pendingStartPlans 待启动的计划列表
     * @return 消息内容，如果列表为空则返回空字符串
     */
    private String buildPendingStartContent(List<String> pendingStartPlans) {
        if (CollectionUtils.isEmpty(pendingStartPlans)) {
            return "";
        }

        StringBuilder content = new StringBuilder();
        content.append("【待启动】有以下产品计划未按开始日期进行启动，请及时启动：\n");
        content.append("----------------\n");
        for (int i = 0; i < pendingStartPlans.size(); i++) {
            content.append(i + 1).append(".").append(pendingStartPlans.get(i)).append("\n");
        }
        return content.toString();
    }

    /**
     * 构建待结束计划的消息内容
     *
     * @param pendingEndPlans 待结束的计划列表
     * @return 消息内容，如果列表为空则返回空字符串
     */
    private String buildPendingEndContent(List<String> pendingEndPlans) {
        if (CollectionUtils.isEmpty(pendingEndPlans)) {
            return "";
        }

        StringBuilder content = new StringBuilder();
        content.append("【待结束】有以下产品计划未及时结束，请及时处理（调整日期或及时结束）：\n");
        content.append("----------------\n");
        for (int i = 0; i < pendingEndPlans.size(); i++) {
            content.append(i + 1).append(".").append(pendingEndPlans.get(i)).append("\n");
        }
        return content.toString();
    }

    /**
     * 按模块分配发送钉钉消息
     * 如果总内容在限制内就直接发送，否则按1:5:5比例分配给三个模块
     *
     * @param noPlanContent 无计划产品内容
     * @param pendingStartContent 待启动计划内容
     * @param pendingEndContent 待结束计划内容
     */
    private void sendDingTalkMessageByModules(String noPlanContent, String pendingStartContent, String pendingEndContent) {
        // 检查是否有任何有效内容
        boolean hasNoPlan = !noPlanContent.isEmpty();
        boolean hasPendingStart = !pendingStartContent.isEmpty();
        boolean hasPendingEnd = !pendingEndContent.isEmpty();

        if (!hasNoPlan && !hasPendingStart && !hasPendingEnd) {
            // 没有任何警告内容，发送正常消息
            sendDingTalkMessage("目前所有产品计划正常推进中。");
            return;
        }

        // 组合完整消息内容
        StringBuilder fullContent = new StringBuilder();
        if (hasNoPlan) {
            fullContent.append(noPlanContent).append("\n");
        }
        if (hasPendingStart) {
            fullContent.append(pendingStartContent).append("\n");
        }
        if (hasPendingEnd) {
            fullContent.append(pendingEndContent);
        }

        String fullMessage = fullContent.toString().trim();

        // 检查完整消息是否超过限制
        final int maxBytesPerMessage = 19000;
        int fullMessageBytes = fullMessage.getBytes(java.nio.charset.StandardCharsets.UTF_8).length;

        log.info("完整消息字节长度：{} bytes", fullMessageBytes);

        if (fullMessageBytes <= maxBytesPerMessage) {
            // 可以一次性发送
            sendDingTalkMessage(fullMessage);
        } else {
            // 需要按模块分配发送
            sendMessagesByModuleAllocation(noPlanContent, pendingStartContent, pendingEndContent, maxBytesPerMessage);
        }
    }

    /**
     * 按模块分配字节数发送消息
     * 无计划产品模块全部返回不截取，剩余字节数平分给其他两个模块
     *
     * @param noPlanContent 无计划产品内容
     * @param pendingStartContent 待启动计划内容
     * @param pendingEndContent 待结束计划内容
     * @param maxBytes 最大字节数
     */
    private void sendMessagesByModuleAllocation(String noPlanContent, String pendingStartContent, String pendingEndContent, int maxBytes) {
        log.info("消息过长，开始按模块分配截断，总可用字节：{}", maxBytes);

        // 无计划产品模块全部返回不截取
        int noPlanBytes = noPlanContent.isEmpty() ? 0 : noPlanContent.getBytes(java.nio.charset.StandardCharsets.UTF_8).length;

        // 计算剩余字节数
        int remainingBytes = maxBytes - noPlanBytes;

        // 预留模块间分隔符的字节数（两个"\n\n"）
        int separatorBytes = 0;
        boolean condition=!noPlanContent.isEmpty() && (!pendingStartContent.isEmpty() || !pendingEndContent.isEmpty());
        if (condition) {
            // 第一个"\n\n"
            separatorBytes += 4;
        }
        if (!pendingStartContent.isEmpty() && !pendingEndContent.isEmpty()) {
            // 第二个"\n\n"
            separatorBytes += 4;
        }

        remainingBytes -= separatorBytes;

        // 如果剩余字节不够，说明无计划模块就已经超出限制
        if (remainingBytes <= 0) {
            log.warn("无计划产品模块内容过长，超出总限制，只发送无计划模块内容");
            sendDingTalkMessage(noPlanContent);
            return;
        }

        // 剩余字节平分给另外两个模块
        int pendingStartAllocation = remainingBytes / 2;
        int pendingEndAllocation = remainingBytes / 2;

        log.info("模块字节分配 - 无计划：{} (全部), 待启动：{}, 待结束：{}",
                noPlanBytes, pendingStartAllocation, pendingEndAllocation);

        // 分别处理每个模块的内容
        StringBuilder finalMessage = new StringBuilder();

        // 无计划模块全部返回
        if (!noPlanContent.isEmpty()) {
            finalMessage.append(noPlanContent);
            finalMessage.append("\n\n");
        }

        // 待启动模块按分配字节数截断
        if (!pendingStartContent.isEmpty()) {
            String truncatedContent = truncateContentByBytes(pendingStartContent, pendingStartAllocation);
            finalMessage.append(truncatedContent);
            if (!pendingEndContent.isEmpty()) {
                finalMessage.append("\n\n");
            }
        }

        // 待结束模块按分配字节数截断
        if (!pendingEndContent.isEmpty()) {
            String truncatedContent = truncateContentByBytes(pendingEndContent, pendingEndAllocation);
            finalMessage.append(truncatedContent);
        }

        // 合并后发送一条消息
        String combinedMessage = finalMessage.toString().trim();
        if (!combinedMessage.isEmpty()) {
            int finalBytes = combinedMessage.getBytes(java.nio.charset.StandardCharsets.UTF_8).length;
            log.info("合并后消息长度：{} bytes", finalBytes);
            if (finalBytes > maxBytes) {
                log.warn("合并后消息仍超过限制，实际：{} bytes，限制：{} bytes", finalBytes, maxBytes);
            }
            sendDingTalkMessage(combinedMessage);
        } else {
            log.warn("所有模块内容处理后都为空，发送默认消息");
            sendDingTalkMessage("目前所有产品计划正常推进中。");
        }
    }

    /**
     * 按字节数截断内容
     * 尽量保持行的完整性
     *
     * @param content 原始内容
     * @param maxBytes 最大字节数
     * @return 截断后的内容
     */
    private String truncateContentByBytes(String content, int maxBytes) {
        if (content.getBytes(java.nio.charset.StandardCharsets.UTF_8).length <= maxBytes) {
            return content;
        }

        String[] lines = content.split("\n");
        StringBuilder result = new StringBuilder();
        boolean truncated = false;

        for (String line : lines) {
            String lineWithNewline = line + "\n";
            int currentBytes = result.toString().getBytes(java.nio.charset.StandardCharsets.UTF_8).length;
            int lineBytes = lineWithNewline.getBytes(java.nio.charset.StandardCharsets.UTF_8).length;

            if (currentBytes + lineBytes <= maxBytes) {
                result.append(lineWithNewline);
            } else {
                // 当前行加上去会超过限制
                String truncateNotice = "\n... 内容过多，已截断 ...";
                int truncateNoticeBytes = truncateNotice.getBytes(java.nio.charset.StandardCharsets.UTF_8).length;

                if (currentBytes + truncateNoticeBytes <= maxBytes) {
                    result.append(truncateNotice);
                }
                truncated = true;
                break;
            }
        }

        String finalContent = result.toString().trim();
        if (truncated) {
            log.warn("模块内容被截断，原始长度：{} bytes，截断后长度：{} bytes",
                    content.getBytes(java.nio.charset.StandardCharsets.UTF_8).length,
                    finalContent.getBytes(java.nio.charset.StandardCharsets.UTF_8).length);
        }

        return finalContent;
    }

    /**
     * 发送单条钉钉消息
     *
     * @param content 消息内容
     */
    private void sendDingTalkMessage(String content) {
        if (content == null || content.isEmpty()) {
            log.warn("消息内容为空，跳过发送");
            return;
        }

        try {
            log.info("开始发送钉钉消息，内容长度：{} 字符，{} bytes",
                    content.length(),
                    content.getBytes(java.nio.charset.StandardCharsets.UTF_8).length);

        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(dingTalkConfig.getPmRobotUrl())
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        messageService.sendBase(robotMsgBo);
        log.info("钉钉消息发送成功");
        } catch (Exception e) {
            log.error("钉钉消息发送失败", e);
            throw e;
        }
    }
}
