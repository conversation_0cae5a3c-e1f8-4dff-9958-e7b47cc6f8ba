package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * 禅道产品计划业务对象 zt_productplan
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ZtProductplanBo extends BaseEntity {

    /**
     * 计划ID
     */
    @NotNull(message = "计划ID不能为空", groups = { EditGroup.class })
    private Integer id;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer product;

    /**
     * 分支
     */
    private String branch;

    /**
     * 父计划
     */
    private Integer parent;

    /**
     * 计划标题
     */
    @NotBlank(message = "计划标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 计划状态
     */
    private String status;

    /**
     * 计划描述
     */
    private String desc;

    /**
     * 开始时间
     */
    private Date begin;

    /**
     * 结束时间
     */
    private Date end;

    /**
     * 完成时间
     */
    private Date finishedDate;

    /**
     * 关闭时间
     */
    private Date closedDate;

    /**
     * 排序
     */
    private String order;

    /**
     * 关闭原因
     */
    private String closedReason;

    /**
     * 创建者
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;

}
