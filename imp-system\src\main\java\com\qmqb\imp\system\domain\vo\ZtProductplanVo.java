package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 禅道产品计划视图对象 zt_productplan
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Data
@ExcelIgnoreUnannotated
public class ZtProductplanVo {

    private static final long serialVersionUID = 1L;

    /**
     * 计划ID
     */
    @ExcelProperty(value = "计划ID")
    private Integer id;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Integer product;

    /**
     * 分支
     */
    @ExcelProperty(value = "分支")
    private String branch;

    /**
     * 父计划
     */
    @ExcelProperty(value = "父计划")
    private Integer parent;

    /**
     * 计划标题
     */
    @ExcelProperty(value = "计划标题")
    private String title;

    /**
     * 计划状态
     */
    @ExcelProperty(value = "计划状态")
    private String status;

    /**
     * 计划描述
     */
    @ExcelProperty(value = "计划描述")
    private String desc;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private Date begin;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private Date end;

    /**
     * 完成时间
     */
    @ExcelProperty(value = "完成时间")
    private Date finishedDate;

    /**
     * 关闭时间
     */
    @ExcelProperty(value = "关闭时间")
    private Date closedDate;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private String order;

    /**
     * 关闭原因
     */
    @ExcelProperty(value = "关闭原因")
    private String closedReason;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createdDate;

}
