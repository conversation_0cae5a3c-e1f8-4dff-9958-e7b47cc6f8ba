package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.system.domain.ZtProductplan;
import com.qmqb.imp.system.domain.vo.ZtProductplanVo;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 禅道产品计划Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@DS(DataSource.ZENTAO)
@InterceptorIgnore(dataPermission = "true")
public interface ZtProductplanMapper extends BaseMapperPlus<ZtProductplanMapper, ZtProductplan, ZtProductplanVo> {

    /**
     * 根据产品ID列表查询产品计划（绕过数据权限拦截器）
     * @param productIds
     * @return
     */
    @InterceptorIgnore(dataPermission = "true")
    List<ZtProductplan> selectByProductIds(@Param("productIds") List<Integer> productIds);

    /**
     * 根据开始时间和状态查询产品计划（绕过数据权限拦截器）
     * @param nowDate
     * @return
     */
    @InterceptorIgnore(dataPermission = "true")
    List<ZtProductplan> selectPendingStartPlans(@Param("nowDate") Date nowDate);

    /**
     * 根据结束时间和状态查询产品计划（绕过数据权限拦截器）
     * @param nowDate
     * @return
     */
    @InterceptorIgnore(dataPermission = "true")
    List<ZtProductplan> selectPendingEndPlans(@Param("nowDate") Date nowDate);

}
