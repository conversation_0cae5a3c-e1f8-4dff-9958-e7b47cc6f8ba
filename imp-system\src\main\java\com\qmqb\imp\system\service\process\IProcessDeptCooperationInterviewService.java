package com.qmqb.imp.system.service.process;

import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.process.ProcessDeptCooperationInterviewBo;
import com.qmqb.imp.system.domain.bo.process.ProcessDeptCooperationInterviewStatisticsBo;
import com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewVo;
import com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewStatisticsVo;

import java.util.List;

/**
 * 部门合作度访谈Service接口
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface IProcessDeptCooperationInterviewService {

    /**
     * 查询部门合作度访谈
     * @param id
     * @return
     */
    ProcessDeptCooperationInterviewVo queryById(Long id);

    /**
     * 查询部门合作度访谈列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<ProcessDeptCooperationInterviewVo> queryPageList(ProcessDeptCooperationInterviewBo bo, PageQuery pageQuery);

    /**
     * 查询部门合作度访谈列表
     * @param bo
     * @return
     */
    List<ProcessDeptCooperationInterviewVo> queryList(ProcessDeptCooperationInterviewBo bo);

    /**
     * 获取部门合作度访谈统计数据
     * @param bo
     * @return
     */
    ProcessDeptCooperationInterviewStatisticsVo getStatistics(ProcessDeptCooperationInterviewStatisticsBo bo);

}
