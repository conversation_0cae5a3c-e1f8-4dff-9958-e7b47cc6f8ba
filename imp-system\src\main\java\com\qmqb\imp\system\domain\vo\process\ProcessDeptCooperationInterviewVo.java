package com.qmqb.imp.system.domain.vo.process;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 部门合作度访谈视图对象 tb_dept_cooperation_interview
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Data
@ExcelIgnoreUnannotated
public class ProcessDeptCooperationInterviewVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 审批编号(businessId)
     */
    @ExcelProperty(value = "审批编号")
    private String approvalId;

    /**
     * 年份
     */
    @ExcelProperty(value = "年份")
    private Integer yearValue;

    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private Integer monthValue;

    /**
     * 类型：1表扬、0待改进
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 被评价人组别ID
     */
    @ExcelProperty(value = "被评价人组别ID")
    private Long groupId;

    /**
     * 被评价人组别名称
     */
    @ExcelProperty(value = "被评价人组别")
    private String groupName;

    /**
     * 被评价人姓名
     */
    @ExcelProperty(value = "被评价人姓名")
    private String employeeName;

    /**
     * 被评价人岗位类型：2技术经理、3开发人员、4测试人员、6项目经理、7运维人员
     */
    @ExcelProperty(value = "被评价人岗位类型")
    private String employeePositionType;

    /**
     * 被评价人岗位名称
     */
    @ExcelProperty(value = "被评价人岗位")
    private String employeePosition;

    /**
     * 事项/问题说明
     */
    @ExcelProperty(value = "事项/问题说明")
    private String description;

    /**
     * 提交人姓名
     */
    @ExcelProperty(value = "提交人姓名")
    private String submitterName;

    /**
     * 提交人所属组ID
     */
    @ExcelProperty(value = "提交人所属组ID")
    private Long submitterGroupId;

    /**
     * 提交人所属组名称
     */
    @ExcelProperty(value = "提交人所属组")
    private String submitterGroup;

    /**
     * 审批结果
     */
    @ExcelProperty(value = "审批结果")
    private String approvalResult;

    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间")
    private Date submitTime;
}
