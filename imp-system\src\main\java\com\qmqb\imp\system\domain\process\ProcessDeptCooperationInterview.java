package com.qmqb.imp.system.domain.process;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 部门合作度访谈对象 tb_dept_cooperation_interview
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_dept_cooperation_interview")
public class ProcessDeptCooperationInterview extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 审批编号(businessId)
     */
    private String approvalId;

    /**
     * 年份
     */
    private Integer yearValue;

    /**
     * 月份
     */
    private Integer monthValue;

    /**
     * 类型：1表扬、0待改进
     */
    private String type;

    /**
     * 被评价人组别ID
     */
    private Long groupId;

    /**
     * 被评价人组别名称
     */
    private String groupName;

    /**
     * 被评价人姓名
     */
    private String employeeName;

    /**
     * 被评价人岗位类型：2技术经理、3开发人员、4测试人员、6项目经理、7运维人员
     */
    private String employeePositionType;

    /**
     * 被评价人岗位名称
     */
    private String employeePosition;

    /**
     * 事项/问题说明
     */
    private String description;

    /**
     * 提交人姓名
     */
    private String submitterName;

    /**
     * 提交人所属组ID
     */
    private Long submitterGroupId;

    /**
     * 提交人所属组名称
     */
    private String submitterGroup;

    /**
     * 审批结果
     */
    private String approvalResult;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;
}
