package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.domain.bo.ZtProductplanBo;
import com.qmqb.imp.system.domain.vo.ZtProductplanVo;
import com.qmqb.imp.system.domain.ZtProductplan;
import com.qmqb.imp.system.mapper.ZtProductplanMapper;
import com.qmqb.imp.system.service.IZtProductplanService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 禅道产品计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@RequiredArgsConstructor
@Service
@InterceptorIgnore(dataPermission = "true", blockAttack = "true")
public class ZtProductplanServiceImpl implements IZtProductplanService {

    private final ZtProductplanMapper baseMapper;

    /**
     * 查询禅道产品计划
     */
    @Override
    public ZtProductplanVo queryById(Integer id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询禅道产品计划列表
     */
    @Override
    public TableDataInfo<ZtProductplanVo> queryPageList(ZtProductplanBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ZtProductplan> lqw = buildQueryWrapper(bo);
        Page<ZtProductplanVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询禅道产品计划列表
     */
    @Override
    public List<ZtProductplanVo> queryList(ZtProductplanBo bo) {
        LambdaQueryWrapper<ZtProductplan> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ZtProductplan> buildQueryWrapper(ZtProductplanBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ZtProductplan> lqw = Wrappers.<ZtProductplan>lambdaQuery();
        lqw.eq(bo.getProduct() != null, ZtProductplan::getProduct, bo.getProduct());
        lqw.like(StringUtils.isNotBlank(bo.getBranch()), ZtProductplan::getBranch, bo.getBranch());
        lqw.eq(bo.getParent() != null, ZtProductplan::getParent, bo.getParent());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), ZtProductplan::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ZtProductplan::getStatus, bo.getStatus());
        lqw.between(params.get("beginBegin") != null && params.get("beginEnd") != null,
            ZtProductplan::getBegin ,params.get("beginBegin"), params.get("beginEnd"));
        lqw.between(params.get("endBegin") != null && params.get("endEnd") != null,
            ZtProductplan::getEnd ,params.get("endBegin"), params.get("endEnd"));
        return lqw;
    }

    /**
     * 新增禅道产品计划
     */
    @Override
    public Boolean insertByBo(ZtProductplanBo bo) {
        ZtProductplan add = BeanUtil.toBean(bo, ZtProductplan.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改禅道产品计划
     */
    @Override
    public Boolean updateByBo(ZtProductplanBo bo) {
        ZtProductplan update = BeanUtil.toBean(bo, ZtProductplan.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ZtProductplan entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除禅道产品计划
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据产品ID查询计划列表
     */
    @Override
    public List<ZtProductplanVo> queryListByProductId(Integer productId) {
        LambdaQueryWrapper<ZtProductplan> lqw = Wrappers.<ZtProductplan>lambdaQuery();
        lqw.eq(ZtProductplan::getProduct, productId);
        lqw.orderByDesc(ZtProductplan::getCreatedDate);
        return baseMapper.selectVoList(lqw);
    }
}
