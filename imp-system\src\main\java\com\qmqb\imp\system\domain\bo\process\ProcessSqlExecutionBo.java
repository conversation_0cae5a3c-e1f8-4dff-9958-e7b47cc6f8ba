package com.qmqb.imp.system.domain.bo.process;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 生产维护SQL执行业务对象 tb_process_sql_execution
 *
 * <AUTHOR>
 * @date 2024-12-17
 */

@Data
@EqualsAndHashCode()
public class ProcessSqlExecutionBo{

    /**
     * 主键
     */
    private Long id;

    /**
     * 审批实例业务编号
     */
    private String processInstanceId;

    /**
     * 标题
     */
    private String title;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 审批结果
     */
    private String result;

    /**
     * 审批状态
     */
    private String status;

    /**
     * 当前负责人
     */
    private String currentResponsiblePerson;

    /**
     * 当前节点
     */
    private String currentNode;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastActionTime;

    /**
     * 创建人
     */
    private String originatorUser;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date originatorTime;

    /**
     * 入库时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更改库时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 提交开始时间
     */
    private String beginDate;

    /**
     * 提交结束时间
     */
    private String endDate;
}
