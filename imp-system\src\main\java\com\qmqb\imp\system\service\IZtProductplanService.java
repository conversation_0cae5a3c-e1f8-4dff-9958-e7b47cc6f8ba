package com.qmqb.imp.system.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.bo.ZtProductplanBo;
import com.qmqb.imp.system.domain.vo.ZtProductplanVo;

import java.util.Collection;
import java.util.List;

/**
 * 禅道产品计划Service接口
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@DS(DataSource.ZENTAO)
public interface IZtProductplanService {

    /**
     * 根据id查询禅道产品计划
     * @param id 产品计划id
     * @return 产品计划信息
     */
    ZtProductplanVo queryById(Integer id);

    /**
     * 分页查询禅道产品计划列表
     * @param bo 查询条件
     * @param pageQuery 分页查询条件
     * @return 产品计划列表
     */
    TableDataInfo<ZtProductplanVo> queryPageList(ZtProductplanBo bo, PageQuery pageQuery);

    /**
     * 查询禅道产品计划列表
     * @param bo 查询条件
     * @return 产品计划列表
     */
    List<ZtProductplanVo> queryList(ZtProductplanBo bo);

    /**
     * 新增禅道产品计划
     * @param bo 新增产品计划信息
     * @return 是否成功
     */
    Boolean insertByBo(ZtProductplanBo bo);

    /**
     * 修改禅道产品计划
     * @param bo 修改产品计划信息
     * @return 是否成功
     */
    Boolean updateByBo(ZtProductplanBo bo);

    /**
     * 校验并批量删除禅道产品计划信息
     * @param ids 产品计划id集合
     * @param isValid 是否有效
     * @return 是否成功
     */
    Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid);

    /**
     * 根据产品ID查询计划列表
     * @param productId 产品id
     * @return 产品计划列表
     */
    List<ZtProductplanVo> queryListByProductId(Integer productId);

}
