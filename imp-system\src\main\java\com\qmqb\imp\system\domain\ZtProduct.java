package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 禅道产品对象 zt_product
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Data
@TableName("zt_product")
public class ZtProduct {

    private static final long serialVersionUID = 1L;

    /**
     * 产品ID
     */
    @TableId(value = "id")
    private Integer id;

    /**
     * 项目集ID
     */
    private Integer program;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品代号
     */
    private String code;

    /**
     * 影子产品
     */
    private Integer shadow;

    /**
     * 绑定
     */
    private String bind;

    /**
     * 产品线
     */
    private Integer line;

    /**
     * 产品类型
     */
    private String type;

    /**
     * 产品状态
     */
    private String status;

    /**
     * 子状态
     */
    @TableField("subStatus")
    private String subStatus;

    /**
     * 产品描述
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 产品负责人
     */
    @TableField("PO")
    private String po;

    /**
     * 测试负责人
     */
    @TableField("QD")
    private String qd;

    /**
     * 发布负责人
     */
    @TableField("RD")
    private String rd;
    /**
     * 反馈负责人
     */
    private String feedback;

    /**
     * 访问控制
     */
    private String acl;

    /**
     * 白名单
     */
    private String whitelist;

    /**
     * 计划数
     */
    private Integer plans;

    /**
     * 创建者
     */
    @TableField("createdBy")
    private String createdBy;
    /**
     *
     */
    @TableField("createdDate")
    private Date createdDate;
    /**
     * 创建版本
     */
    @TableField("createdVersion")
    private String createdVersion;

    /**
     * 排序
     */
    @TableField("`order`")
    private Integer order;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    private String deleted;

}
