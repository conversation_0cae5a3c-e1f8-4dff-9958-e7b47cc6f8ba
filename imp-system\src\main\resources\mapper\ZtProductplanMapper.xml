<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ZtProductplanMapper">

    <!-- 根据产品ID列表查询产品计划（绕过数据权限拦截器） -->
    <select id="selectByProductIds" resultType="com.qmqb.imp.system.domain.ZtProductplan">
        SELECT id,product,branch,parent,title,status,`desc`,begin,end,finishedDate,closedDate,`order`,closedReason,createdBy,createdDate,deleted
        FROM zt_productplan
        WHERE deleted = '0'
        <if test="productIds != null and productIds.size() > 0">
            AND product IN
            <foreach collection="productIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 根据开始时间和状态查询产品计划（绕过数据权限拦截器） -->
    <select id="selectPendingStartPlans" resultType="com.qmqb.imp.system.domain.ZtProductplan">
        SELECT id,product,branch,parent,title,status,`desc`,begin,end,finishedDate,closedDate,`order`,closedReason,createdBy,createdDate,deleted
        FROM zt_productplan
        WHERE begin <![CDATA[ < ]]> #{nowDate}
        AND status = 'wait'
        AND deleted = '0'
        AND createdDate >= '2025-01-01 00:00:00'
    </select>

    <!-- 根据结束时间和状态查询产品计划（绕过数据权限拦截器） -->
    <select id="selectPendingEndPlans" resultType="com.qmqb.imp.system.domain.ZtProductplan">
        SELECT id,product,branch,parent,title,status,`desc`,begin,end,finishedDate,closedDate,`order`,closedReason,createdBy,createdDate,deleted
        FROM zt_productplan
        WHERE end <![CDATA[ < ]]> #{nowDate}
        AND status NOT IN ('done', 'closed')
        AND deleted = '0'
        AND createdDate >= '2025-01-01 00:00:00'
    </select>

</mapper>
