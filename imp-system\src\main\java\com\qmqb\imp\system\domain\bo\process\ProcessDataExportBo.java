package com.qmqb.imp.system.domain.bo.process;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 业务敏感数据导出申请业务对象 tb_process_data_export
 *
 * <AUTHOR>
 * @date 2024-12-18
 */

@Data
public class ProcessDataExportBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 审批实例业务编号
     */
    private String processInstanceId;

    /**
     * 标题
     */
    private String title;

    /**
     * 涉及敏感数据项
     */
    private String dataEnvironment;

    /**
     * 导出用途
     */
    private String exportPurpose;

    /**
     * 期望导出日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedExportDate;

    /**
     * 审批结果
     */
    private String result;

    /**
     * 审批状态
     */
    private String status;

    /**
     * 当前节点
     */
    private String currentNode;

    /**
     * 当前负责人
     */
    private String currentResponsiblePerson;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastActionTime;

    /**
     * 创建人
     */
    private String originatorUser;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date originatorTime;

    /**
     * 创建时间范围
     */
    private Date[] originatorTimeRange;




}
