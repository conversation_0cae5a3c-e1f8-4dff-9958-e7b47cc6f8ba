package com.qmqb.imp.system.domain.bo.process;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 部门合作度访谈统计查询对象
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Data
public class ProcessDeptCooperationInterviewStatisticsBo {

    /**
     * 年份（必传）
     */
    @NotNull(message = "年份不能为空")
    private Integer yearValue;

    /**
     * 月份（可选，如果传了则按天统计，不传则按月统计）
     */
    private Integer monthValue;
}
