package com.qmqb.imp.system.domain.vo.process;

import lombok.Data;

import java.util.List;

/**
 * 部门合作度访谈统计视图对象
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Data
public class ProcessDeptCooperationInterviewStatisticsVo {

    /**
     * 基础统计数据
     */
    private BasicStatistics basicStatistics;

    /**
     * 个人被表扬次数TOP10
     */
    private List<PersonalRanking> praiseTop10;

    /**
     * 个人待改进次数TOP10
     */
    private List<PersonalRanking> improvementTop10;

    /**
     * 时间趋势数据
     */
    private List<TimeTrend> timeTrends;

    /**
     * 按组统计数据
     */
    private List<GroupStatistics> groupStatistics;

    /**
     * 按岗位统计数据
     */
    private List<PositionStatistics> positionStatistics;

    /**
     * 基础统计数据
     */
    @Data
    public static class BasicStatistics {
        /**
         * 提交数（按审批编号去重）
         */
        private Integer submitCount;

        /**
         * 表扬数（type=1）
         */
        private Integer praiseCount;

        /**
         * 待改进数（type=0）
         */
        private Integer improvementCount;
    }

    /**
     * 个人排行榜
     */
    @Data
    public static class PersonalRanking {
        /**
         * 排名
         */
        private Integer ranking;

        /**
         * 姓名
         */
        private String employeeName;

        /**
         * 次数
         */
        private Integer count;
    }

    /**
     * 时间趋势
     */
    @Data
    public static class TimeTrend {
        /**
         * 时间标签（月份或日期）
         */
        private String timeLabel;

        /**
         * 表扬数量
         */
        private Integer praiseCount;

        /**
         * 待改进数量
         */
        private Integer improvementCount;
    }

    /**
     * 按组统计
     */
    @Data
    public static class GroupStatistics {
        /**
         * 组名称
         */
        private String groupName;

        /**
         * 表扬数量
         */
        private Integer praiseCount;

        /**
         * 待改进数量
         */
        private Integer improvementCount;
    }

    /**
     * 按岗位统计
     */
    @Data
    public static class PositionStatistics {
        /**
         * 岗位名称
         */
        private String positionName;

        /**
         * 表扬数量
         */
        private Integer praiseCount;

        /**
         * 待改进数量
         */
        private Integer improvementCount;
    }
}
