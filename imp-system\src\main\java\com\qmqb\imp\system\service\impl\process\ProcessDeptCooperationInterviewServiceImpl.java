package com.qmqb.imp.system.service.impl.process;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.bo.process.ProcessDeptCooperationInterviewBo;
import com.qmqb.imp.system.domain.process.ProcessDeptCooperationInterview;
import com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewVo;
import com.qmqb.imp.system.mapper.process.ProcessDeptCooperationInterviewMapper;
import com.qmqb.imp.system.service.process.IProcessDeptCooperationInterviewService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 部门合作度访谈Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@RequiredArgsConstructor
@Service
public class ProcessDeptCooperationInterviewServiceImpl implements IProcessDeptCooperationInterviewService {

    private final ProcessDeptCooperationInterviewMapper baseMapper;

    /**
     * 查询部门合作度访谈
     */
    @Override
    public ProcessDeptCooperationInterviewVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询部门合作度访谈列表
     */
    @Override
    public TableDataInfo<ProcessDeptCooperationInterviewVo> queryPageList(ProcessDeptCooperationInterviewBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProcessDeptCooperationInterview> lqw = buildQueryWrapper(bo);
        Page<ProcessDeptCooperationInterviewVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询部门合作度访谈列表
     */
    @Override
    public List<ProcessDeptCooperationInterviewVo> queryList(ProcessDeptCooperationInterviewBo bo) {
        LambdaQueryWrapper<ProcessDeptCooperationInterview> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProcessDeptCooperationInterview> buildQueryWrapper(ProcessDeptCooperationInterviewBo bo) {
        LambdaQueryWrapper<ProcessDeptCooperationInterview> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalId()), ProcessDeptCooperationInterview::getApprovalId, bo.getApprovalId());
        lqw.eq(bo.getYearValue() != null, ProcessDeptCooperationInterview::getYearValue, bo.getYearValue());
        lqw.eq(bo.getMonthValue() != null, ProcessDeptCooperationInterview::getMonthValue, bo.getMonthValue());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ProcessDeptCooperationInterview::getType, bo.getType());
        lqw.eq(bo.getGroupId() != null, ProcessDeptCooperationInterview::getGroupId, bo.getGroupId());
        lqw.like(StringUtils.isNotBlank(bo.getGroupName()), ProcessDeptCooperationInterview::getGroupName, bo.getGroupName());
        lqw.like(StringUtils.isNotBlank(bo.getEmployeeName()), ProcessDeptCooperationInterview::getEmployeeName, bo.getEmployeeName());
        lqw.eq(StringUtils.isNotBlank(bo.getEmployeePositionType()), ProcessDeptCooperationInterview::getEmployeePositionType, bo.getEmployeePositionType());
        lqw.like(StringUtils.isNotBlank(bo.getEmployeePosition()), ProcessDeptCooperationInterview::getEmployeePosition, bo.getEmployeePosition());
        lqw.like(StringUtils.isNotBlank(bo.getDescription()), ProcessDeptCooperationInterview::getDescription, bo.getDescription());
        lqw.like(StringUtils.isNotBlank(bo.getSubmitterName()), ProcessDeptCooperationInterview::getSubmitterName, bo.getSubmitterName());
        lqw.eq(bo.getSubmitterGroupId() != null, ProcessDeptCooperationInterview::getSubmitterGroupId, bo.getSubmitterGroupId());
        lqw.like(StringUtils.isNotBlank(bo.getSubmitterGroup()), ProcessDeptCooperationInterview::getSubmitterGroup, bo.getSubmitterGroup());
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalResult()), ProcessDeptCooperationInterview::getApprovalResult, bo.getApprovalResult());
        lqw.between(bo.getSubmitTimeStart() != null && bo.getSubmitTimeEnd() != null,
            ProcessDeptCooperationInterview::getSubmitTime, bo.getSubmitTimeStart(), bo.getSubmitTimeEnd());
        lqw.orderByDesc(ProcessDeptCooperationInterview::getSubmitTime);
        return lqw;
    }


}
