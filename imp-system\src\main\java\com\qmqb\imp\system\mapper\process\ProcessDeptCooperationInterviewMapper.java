package com.qmqb.imp.system.mapper.process;

import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.process.ProcessDeptCooperationInterview;
import com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewVo;
import com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewStatisticsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门合作度访谈Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface ProcessDeptCooperationInterviewMapper extends BaseMapperPlus<ProcessDeptCooperationInterviewMapper, ProcessDeptCooperationInterview, ProcessDeptCooperationInterviewVo> {

    /**
     * 批量插入或更新
     * @param list 数据列表
     * @return 影响行数
     */
    int batchInsertOnUpdate(List<ProcessDeptCooperationInterview> list);

    /**
     * 获取基础统计数据
     * @param yearValue 年份
     * @param monthValue 月份（可选）
     * @return 基础统计数据
     */
    ProcessDeptCooperationInterviewStatisticsVo.BasicStatistics getBasicStatistics(@Param("yearValue") Integer yearValue, @Param("monthValue") Integer monthValue);

    /**
     * 获取个人被表扬次数TOP10
     * @param yearValue 年份
     * @param monthValue 月份（可选）
     * @return 个人表扬排行榜
     */
    List<ProcessDeptCooperationInterviewStatisticsVo.PersonalRanking> getPraiseTop10(@Param("yearValue") Integer yearValue, @Param("monthValue") Integer monthValue);

    /**
     * 获取个人待改进次数TOP10
     * @param yearValue 年份
     * @param monthValue 月份（可选）
     * @return 个人待改进排行榜
     */
    List<ProcessDeptCooperationInterviewStatisticsVo.PersonalRanking> getImprovementTop10(@Param("yearValue") Integer yearValue, @Param("monthValue") Integer monthValue);

    /**
     * 获取按月统计的时间趋势数据
     * @param yearValue 年份
     * @return 按月统计的时间趋势
     */
    List<ProcessDeptCooperationInterviewStatisticsVo.TimeTrend> getMonthlyTimeTrends(@Param("yearValue") Integer yearValue);

    /**
     * 获取按天统计的时间趋势数据
     * @param yearValue 年份
     * @param monthValue 月份
     * @return 按天统计的时间趋势
     */
    List<ProcessDeptCooperationInterviewStatisticsVo.TimeTrend> getDailyTimeTrends(@Param("yearValue") Integer yearValue, @Param("monthValue") Integer monthValue);

    /**
     * 获取按组统计数据
     * @param yearValue 年份
     * @param monthValue 月份（可选）
     * @return 按组统计数据
     */
    List<ProcessDeptCooperationInterviewStatisticsVo.GroupStatistics> getGroupStatistics(@Param("yearValue") Integer yearValue, @Param("monthValue") Integer monthValue);

    /**
     * 获取按岗位统计数据
     * @param yearValue 年份
     * @param monthValue 月份（可选）
     * @return 按岗位统计数据
     */
    List<ProcessDeptCooperationInterviewStatisticsVo.PositionStatistics> getPositionStatistics(@Param("yearValue") Integer yearValue, @Param("monthValue") Integer monthValue);
}
