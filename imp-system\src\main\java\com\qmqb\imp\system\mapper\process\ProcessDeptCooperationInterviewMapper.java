package com.qmqb.imp.system.mapper.process;

import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.process.ProcessDeptCooperationInterview;
import com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewVo;

import java.util.List;

/**
 * 部门合作度访谈Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface ProcessDeptCooperationInterviewMapper extends BaseMapperPlus<ProcessDeptCooperationInterviewMapper, ProcessDeptCooperationInterview, ProcessDeptCooperationInterviewVo> {
    
    /**
     * 批量插入或更新
     * @param list 数据列表
     * @return 影响行数
     */
    int batchInsertOnUpdate(List<ProcessDeptCooperationInterview> list);
}
