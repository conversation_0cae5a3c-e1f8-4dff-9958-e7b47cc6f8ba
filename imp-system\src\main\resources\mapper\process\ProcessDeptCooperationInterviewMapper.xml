<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.process.ProcessDeptCooperationInterviewMapper">

    <resultMap type="com.qmqb.imp.system.domain.process.ProcessDeptCooperationInterview" id="ProcessDeptCooperationInterviewResult">
        <result property="id" column="id"/>
        <result property="approvalId" column="approval_id"/>
        <result property="yearValue" column="year_value"/>
        <result property="monthValue" column="month_value"/>
        <result property="type" column="type"/>
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="employeeName" column="employee_name"/>
        <result property="employeePositionType" column="employee_position_type"/>
        <result property="employeePosition" column="employee_position"/>
        <result property="description" column="description"/>
        <result property="submitterName" column="submitter_name"/>
        <result property="submitterGroupId" column="submitter_group_id"/>
        <result property="submitterGroup" column="submitter_group"/>
        <result property="approvalResult" column="approval_result"/>
        <result property="submitTime" column="submit_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <insert id="batchInsertOnUpdate">
        INSERT INTO tb_dept_cooperation_interview(
            `approval_id`, `year_value`, `month_value`, `type`, `group_id`, `group_name`,
            `employee_name`, `employee_position_type`, `employee_position`, `description`,
            `submitter_name`, `submitter_group_id`, `submitter_group`, `approval_result`,
            `submit_time`, `del_flag`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.approvalId}, #{item.yearValue}, #{item.monthValue}, #{item.type},
                #{item.groupId}, #{item.groupName}, #{item.employeeName},
                #{item.employeePositionType}, #{item.employeePosition}, #{item.description},
                #{item.submitterName}, #{item.submitterGroupId}, #{item.submitterGroup},
                #{item.approvalResult}, #{item.submitTime}, #{item.delFlag}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            `year_value` = VALUES(`year_value`),
            `month_value` = VALUES(`month_value`),
            `type` = VALUES(`type`),
            `group_id` = VALUES(`group_id`),
            `group_name` = VALUES(`group_name`),
            `employee_name` = VALUES(`employee_name`),
            `employee_position_type` = VALUES(`employee_position_type`),
            `employee_position` = VALUES(`employee_position`),
            `description` = VALUES(`description`),
            `submitter_name` = VALUES(`submitter_name`),
            `submitter_group_id` = VALUES(`submitter_group_id`),
            `submitter_group` = VALUES(`submitter_group`),
            `approval_result` = VALUES(`approval_result`),
            `submit_time` = VALUES(`submit_time`)
    </insert>

</mapper>
