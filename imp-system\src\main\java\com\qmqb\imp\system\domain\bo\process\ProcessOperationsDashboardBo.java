package com.qmqb.imp.system.domain.bo.process;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 运营看板及报表需求业务对象 tb_process_operations_dashboard
 *
 * <AUTHOR>
 * @date 2024-12-19
 */

@Data
@EqualsAndHashCode()
public class ProcessOperationsDashboardBo{

    /**
     * 主键
     */
    private Long id;

    /**
     * 审批实例业务编号
     */
    private String processInstanceId;

    /**
     * 业务大类
     */
    private String businessCategory;

    /**
     * 看板类型
     */
    private String boardType;

    /**
     * 需求标题
     */
    private String requirementTitle;

    /**
     * 期望完成日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedCompletionDate;

    /**
     * 需求优先度
     */
    private String priorityLevel;

    /**
     * 审批结果
     */
    private String result;

    /**
     * 审批状态
     */
    private String status;

    /**
     * 当前负责人
     */
    private String currentResponsiblePerson;

    /**
     * 当前节点
     */
    private String currentNode;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastActionTime;

    /**
     * 创建人
     */
    private String originatorUser;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date originatorTime;

    /**
     * 入库时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更改库时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 提交开始时间
     */
    private String beginDate;

    /**
     * 提交结束时间
     */
    private String endDate;
}
