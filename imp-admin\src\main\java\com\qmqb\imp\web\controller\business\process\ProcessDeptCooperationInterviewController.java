package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.process.ProcessDeptCooperationInterviewBo;
import com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewVo;
import com.qmqb.imp.system.service.process.IProcessDeptCooperationInterviewService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 部门合作度访谈
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/process/deptCooperationInterview")
public class ProcessDeptCooperationInterviewController extends BaseController {

    private final IProcessDeptCooperationInterviewService iProcessDeptCooperationInterviewService;

    /**
     * 查询部门合作度访谈列表
     */
    @SaCheckPermission("business:deptCooperationInterview:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessDeptCooperationInterviewVo> list(ProcessDeptCooperationInterviewBo bo, PageQuery pageQuery) {
        return iProcessDeptCooperationInterviewService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出部门合作度访谈列表
     */
    @SaCheckPermission("business:deptCooperationInterview:export")
    @Log(title = "部门合作度访谈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProcessDeptCooperationInterviewBo bo, HttpServletResponse response) {
        List<ProcessDeptCooperationInterviewVo> list = iProcessDeptCooperationInterviewService.queryList(bo);
        ExcelUtil.exportExcel(list, "部门合作度访谈", ProcessDeptCooperationInterviewVo.class, response);
    }

    /**
     * 获取部门合作度访谈详细信息
     */
    @SaCheckPermission("business:deptCooperationInterview:query")
    @GetMapping("/{id}")
    public R<ProcessDeptCooperationInterviewVo> getInfo(@NotNull(message = "主键不能为空")
                                                         @PathVariable Long id) {
        return R.ok(iProcessDeptCooperationInterviewService.queryById(id));
    }


}
