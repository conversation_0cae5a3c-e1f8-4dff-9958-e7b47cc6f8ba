package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 禅道产品计划对象 zt_productplan
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Data
@TableName("zt_productplan")
public class ZtProductplan {

    private static final long serialVersionUID = 1L;

    /**
     * 计划ID
     */
    @TableId(value = "id")
    private Integer id;

    /**
     * 产品ID
     */
    private Integer product;

    /**
     * 分支
     */
    private String branch;

    /**
     * 父计划
     */
    private Integer parent;

    /**
     * 计划标题
     */
    private String title;

    /**
     * 计划状态
     */
    private String status;

    /**
     * 计划描述
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 开始时间
     */
    private Date begin;

    /**
     * 结束时间
     */
    private Date end;

    /**
     * 完成时间
     */
    @TableField("finishedDate")
    private Date finishedDate;

    /**
     * 关闭时间
     */
    @TableField("closedDate")
    private Date closedDate;

    /**
     * 排序
     */
    @TableField("`order`")
    private String order;

    /**
     * 关闭原因
     */
    @TableField("closedReason")
    private String closedReason;

    /**
     * 创建者
     */
    @TableField("createdBy")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("createdDate")
    private Date createdDate;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    private String deleted;

}
